{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load django_permissions %}
{% load form_utils %}

{% block title %}{{ employee.emp_full_name }} - تفاصيل الموظف{% endblock %}

{% block page_title %}تفاصيل الموظف{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ employee.emp_full_name|default:"موظف" }}</li>
{% endblock %}

{% block content %}
<!-- شريط الإجراءات -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0">{{ employee.emp_full_name }}</h4>
        <p class="text-muted mb-0">{{ employee.jop_name|default:"No Job Assigned" }}</p>
    </div>
    <div class="btn-group">
        {% if perms.Hr.change_employee or user|is_admin %}
            <a href="{% url 'Hr:employees:edit' emp_id=employee.emp_id %}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        {% endif %}
        {% if perms.Hr.view_employee or user|is_admin %}
            <a href="{% url 'Hr:employees:print' emp_id=employee.emp_id %}" class="btn btn-info" target="_blank">
                <i class="fas fa-print me-1"></i> طباعة
            </a>
        {% endif %}
        {% if perms.Hr.delete_employee or user|is_admin %}
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEmployeeModal">
                <i class="fas fa-trash me-1"></i> حذف
            </button>
        {% endif %}
    </div>
</div>

<!-- المعلومات العامة -->
<div class="row">
    <!-- البيانات الشخصية -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-body text-center pt-4">
                {% if employee.emp_image %}
                <div class="employee-avatar mx-auto position-relative mb-3">
                    <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle user-image">
                    {% if employee.working_condition == 'سارى' %}
                    <span class="status-badge bg-success"><i class="fas fa-check"></i></span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="status-badge bg-info"><i class="fas fa-pause"></i></span>
                    {% elif employee.working_condition == 'انقطاع عن العمل'%}
                    <span class="status-badge bg-danger"><i class="fas fa-times"></i></span>
                    {% endif %}
                </div>
                {% else %}
                <div class="employee-avatar-placeholder mx-auto position-relative mb-3">
                    <div class="avatar-placeholder display-1">
                        {{ employee.emp_first_name|slice:":1"|upper }}
                    </div>
                    {% if employee.working_condition == 'سارى' %}
                    <span class="status-badge bg-success"><i class="fas fa-check"></i></span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="status-badge bg-info"><i class="fas fa-pause"></i></span>
                    {% elif employee.working_condition == 'انقطاع عن العمل'%}
                    <span class="status-badge bg-danger"><i class="fas fa-times"></i></span>
                    {% endif %}
                </div>
                {% endif %}

                <h5 class="mt-3 mb-1">{{ employee.emp_full_name }}</h5>
                <p class="text-muted">{{ employee.job_name|default:"-" }}</p>

                <hr>

                <div class="row text-start g-3 mt-2">
                    <div class="col-6">
                        <h6 class="text-muted mb-1">رقم الموظف</h6>
                        <p class="mb-0">{{ employee.emp_id }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">الحالة</h6>
                        <p class="mb-0">{{ employee.working_condition }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">الرقم القومي</h6>
                        <p class="mb-0">{{ employee.national_id|default:"-" }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">تاريخ التعيين</h6>
                        <p class="mb-0">{% if employee.emp_date_hiring %}{{ employee.emp_date_hiring|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                </div>

                <div class="mt-3">
                    <h6 class="text-muted mb-2">بيانات الاتصال</h6>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone-alt text-muted me-2"></i>
                        <span>{{ employee.emp_phone1|default:"-" }}</span>
                    </div>
                    {% if employee.emp_phone2 %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone-alt text-muted me-2"></i>
                        <span>{{ employee.emp_phone2 }}</span>
                    </div>
                    {% endif %}
                    <div class="d-flex align-items-center">
                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                        <span>{{ employee.emp_address|default:"-" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- البيانات التفصيلية -->
    <div class="col-lg-8">
        <!-- بيانات العمل والتوظيف -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    معلومات العمل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">القسم</h6>
                        <p>{{ employee.department.dept_name|default:"-" }}</p>
                    </div>                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الوظيفة</h6>
                        <p>{{ employee.jop_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">نوع الموظف</h6>
                        <p>{{ employee.emp_type|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ التعيين</h6>
                        <p>{% if employee.emp_date_hiring %}{{ employee.emp_date_hiring|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ تجديد العقد</h6>
                        <p>{% if employee.contract_renewal_date %}{{ employee.contract_renewal_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الوردية</h6>
                        <p>{{ employee.shift_type|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وردية الأسبوع الحالي</h6>
                        <p>{{ employee.current_week_shift|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وردية الأسبوع القادم</h6>
                        <p>{{ employee.next_week_shift|default:"-" }}</p>
                    </div>
                </div>

                {% if employee.working_condition == 'استقالة' %}
                <hr>
                <h6 class="text-danger mb-2">بيانات الاستقالة</h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted mb-1">تاريخ الاستقالة</h6>
                        <p>{% if employee.date_resignation %}{{ employee.date_resignation|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted mb-1">سبب الاستقالة</h6>
                        <p>{{ employee.reason_resignation|default:"-" }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- بيانات التأمين -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    التأمين والبطاقة الصحية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">حالة التأمين</h6>
                        <p>{{ employee.insurance_status|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وظيفة التأمين</h6>
                        <p>{{ employee.job_insurance.job_name_insurance|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">رقم التأمين</h6>
                        <p>{{ employee.number_insurance|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ بداية التأمين</h6>
                        <p>{% if employee.date_insurance_start %}{{ employee.date_insurance_start|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">راتب التأمين</h6>
                        <p>{{ employee.insurance_salary|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">نسبة التأمين المستحق</h6>
                        <p>{{ employee.percentage_insurance_payable|default:"-" }}</p>
                    </div>
                </div>

                <hr>

                <h6 class="text-primary mb-2">بيانات البطاقة الصحية</h6>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">البطاقة الصحية</h6>
                        <p>{{ employee.health_card|default:"-" }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">رقم البطاقة</h6>
                        <p>{{ employee.health_card_number|default:"-" }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">تاريخ البداية</h6>
                        <p>{% if employee.health_card_start_date %}{{ employee.health_card_start_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">تاريخ التجديد</h6>
                        <p>{% if employee.health_card_renewal_date %}{{ employee.health_card_renewal_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات شخصية إضافية -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات شخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الاسم الأول</h6>
                        <p>{{ employee.emp_first_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الاسم الثاني</h6>
                        <p>{{ employee.emp_second_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">اسم الأم</h6>
                        <p>{{ employee.mother_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الاسم بالإنجليزية</h6>
                        <p>{{ employee.emp_name_english|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الجنسية</h6>
                        <p>{{ employee.emp_nationality|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الحالة الاجتماعية</h6>
                        <p>{{ employee.emp_marital_status|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ الميلاد</h6>
                        <p>{% if employee.date_birth %}{{ employee.date_birth|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">محل الميلاد</h6>
                        <p>{{ employee.place_birth|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">المحافظة</h6>
                        <p>{{ employee.governorate|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">شهادة الخدمة العسكرية</h6>
                        <p>{{ employee.military_service_certificate|default:"-" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الحذف -->
<div class="modal fade" id="deleteEmployeeModal" tabindex="-1" aria-labelledby="deleteEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEmployeeModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف بيانات الموظف <strong>{{ employee.emp_full_name }}</strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{% url 'Hr:employees:delete' employee.emp_id %}" method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">نعم، حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>    /* Variables */
    :root {
        --primary-gradient-start: #FF6B6B;
        --primary-gradient-end: #FF8E53;
        --secondary-gradient-start: #4E54C8;
        --secondary-gradient-end: #8F94FB;
        --success-gradient-start: #11998e;
        --success-gradient-end: #38ef7d;
        --info-gradient-start: #21D4FD;
        --info-gradient-end: #2152FF;
        --warning-gradient-start: #FFA07A;
        --warning-gradient-end: #FFD700;
        --danger-gradient-start: #FF416C;
        --danger-gradient-end: #FF4B2B;
        --text-primary: #2c3e50;
        --text-secondary: #546e7a;
        --text-muted: #78909c;
        --border-radius: 12px;
        --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --transition: all 0.3s ease;
    }

    /* صورة الموظف وبيانات البطاقة الشخصية */
    .employee-avatar {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: 5px solid #fff;
        transition: var(--transition);
        margin-bottom: 1.5rem;
        position: relative;
    }
    
    .employee-avatar:hover {
        transform: scale(1.05);
    }

    .employee-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .employee-avatar-placeholder {
        width: 150px;
        height: 150px;
        margin-bottom: 1.5rem;
    }

    .avatar-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: linear-gradient(45deg, #0062cc, #0096ff);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3.5rem;
        font-weight: 300;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 4px solid #fff;
    }

    .status-badge {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
      /* تنسيقات البطاقات */
    .card {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        background: #fff;
        overflow: hidden;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }    .card-header {
        background: #fff;
        padding: 1.25rem;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .card-header h5 {
        color: var(--text-primary) !important;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    /* استايل خاص لأيقونات البطاقات فقط */
    .card .card-header i.fas {
        background: linear-gradient(135deg, var(--primary-gradient-start), var(--primary-gradient-end));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        width: 40px;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        margin-left: 12px;
        font-size: 1.2em;
        position: relative;
    }

    /* تنسيقات مختلفة لكل بطاقة */
    .card:nth-of-type(1) .card-header i.fas {
        background: linear-gradient(135deg, var(--info-gradient-start), var(--info-gradient-end));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .card:nth-of-type(2) .card-header i.fas {
        background: linear-gradient(135deg, var(--success-gradient-start), var(--success-gradient-end));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .card:nth-of-type(3) .card-header i.fas {
        background: linear-gradient(135deg, var(--warning-gradient-start), var(--warning-gradient-end));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    /* إضافة تأثير خلفية خفيف للأيقونات */
    .card .card-header i.fas::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: currentColor;
        opacity: 0.1;
        border-radius: 10px;
        z-index: -1;
    }

    .card-body {
        padding: 1.5rem;
    }
    
    .card-header {
        border-top-left-radius: 10px !important;
        border-top-right-radius: 10px !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
      /* تحسين مظهر العناوين والنصوص */
    h5.mb-0 {
        font-weight: 700;
        color: var(--text-primary);
        font-size: 1.25rem;
    }
    
    h6.text-muted {
        font-size: 0.9rem;
        color: var(--text-secondary) !important;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    p {
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        font-size: 1rem;
        line-height: 1.6;
    }

    /* تحسينات للبيانات */
    .info-group {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .info-group:hover {
        background: #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }    /* الأيقونات */
    .card i.fas {
        width: 40px;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        margin-left: 12px;
        font-size: 1.2em;
        position: relative;
        background: linear-gradient(135deg, var(--primary-gradient-start), var(--primary-gradient-end));
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
    }

    /* تنسيقات خاصة لكل نوع من الأيقونات */
    .card i.fa-briefcase {
        background: linear-gradient(135deg, var(--info-gradient-start), var(--info-gradient-end));
        background-clip: text;
        -webkit-background-clip: text;
    }

    .card i.fa-shield-alt {
        background: linear-gradient(135deg, var(--success-gradient-start), var(--success-gradient-end));
        background-clip: text;
        -webkit-background-clip: text;
    }

    .card i.fa-user {
        background: linear-gradient(135deg, var(--warning-gradient-start), var(--warning-gradient-end));
        background-clip: text;
        -webkit-background-clip: text;
    }

    /* إضافة خلفية خفيفة للأيقونات */
    .card i.fas::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: currentColor;
        opacity: 0.08;
        border-radius: 10px;
        z-index: -1;
    }
      /* تنسيق أزرار الإجراءات */
    .btn-group .btn {
        padding: 0.6rem 1.2rem;
        border-radius: 8px;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-right: 0.5rem;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-group .btn i {
        margin-left: 8px;
        font-size: 0.9em;
    }

    .btn-group .btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
    }

    .btn-group .btn:hover::after {
        width: 200%;
        height: 200%;
    }
    
    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    
    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
    }
    
    .btn-info {
        background-color: #0dcaf0;
        border-color: #0dcaf0;
        color: #fff;
    }
    
    .btn-info:hover {
        background-color: #31d2f2;
        border-color: #25cff2;
        color: #fff;
    }
    
    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .btn-danger:hover {
        background-color: #bb2d3b;
        border-color: #b02a37;
    }
    
    /* تحسينات أخرى */
    .text-muted {
        color: #6c757d !important;
    }
    
    hr {
        margin: 1.5rem 0;
        background-color: rgba(0, 0, 0, 0.1);
        opacity: 0.5;
    }
    
    i.fas {
        color: #0d6efd;
    }
    
    .modal-content {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .modal-header {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background-color: #f8f9fa;
    }
    
    .modal-footer {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }
</style>
{% endblock %}
