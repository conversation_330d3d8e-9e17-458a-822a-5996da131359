{% extends 'base_updated.html' %}
{% load static %}

{% block title %}إعدادات الذكاء الاصطناعي - نظام الدولية{% endblock %}

{% block page_title %}إعدادات الذكاء الاصطناعي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'api:dashboard' %}">API</a></li>
<li class="breadcrumb-item active">إعدادات AI</li>
{% endblock %}

{% block content %}
<style>
    .provider-card {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .provider-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    }
    
    .provider-card.configured {
        border-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
    }
    
    .provider-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
    }
    
    .status-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .config-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .test-result {
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 8px;
        display: none;
    }
    
    .test-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .test-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
</style>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">إدارة إعدادات الذكاء الاصطناعي</h5>
                        <p class="text-muted mb-0">قم بإعداد مفاتيح API لمختلف مقدمي خدمات الذكاء الاصطناعي</p>
                    </div>
                    <a href="{% url 'api:add_ai_config' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة إعداد جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Providers Grid -->
<div class="row g-4">
    {% for provider in providers %}
    <div class="col-lg-4 col-md-6">
        {% with user_configs|dictsort:"provider.name" as sorted_configs %}
        {% for config in user_configs %}
            {% if config.provider.id == provider.id %}
                <!-- Configured Provider -->
                <div class="card provider-card configured position-relative">
                    <span class="status-badge bg-success text-white">
                        {% if config.is_default %}افتراضي{% else %}مُعد{% endif %}
                    </span>
                    
                    <div class="card-body text-center">
                        <div class="provider-icon bg-success bg-opacity-10">
                            {% if provider.name == 'gemini' %}
                                <i class="fab fa-google text-success"></i>
                            {% elif provider.name == 'openai' %}
                                <i class="fas fa-brain text-success"></i>
                            {% elif provider.name == 'claude' %}
                                <i class="fas fa-robot text-success"></i>
                            {% elif provider.name == 'huggingface' %}
                                <i class="fas fa-face-smile text-success"></i>
                            {% elif provider.name == 'ollama' %}
                                <i class="fas fa-server text-success"></i>
                            {% else %}
                                <i class="fas fa-cog text-success"></i>
                            {% endif %}
                        </div>
                        
                        <h5>{{ provider.display_name }}</h5>
                        <p class="text-muted small">{{ provider.description|default:"مقدم خدمة ذكاء اصطناعي" }}</p>
                        
                        <div class="config-details">
                            <div class="row text-start">
                                <div class="col-6">
                                    <small class="text-muted">النموذج:</small>
                                    <br><strong>{{ config.model_name }}</strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الحالة:</small>
                                    <br>
                                    {% if config.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </div>
                                <div class="col-6 mt-2">
                                    <small class="text-muted">الرموز:</small>
                                    <br><strong>{{ config.max_tokens }}</strong>
                                </div>
                                <div class="col-6 mt-2">
                                    <small class="text-muted">الإبداع:</small>
                                    <br><strong>{{ config.temperature }}</strong>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="testConfig({{ config.id }}, '{{ provider.display_name }}')">
                                    <i class="fas fa-vial"></i> اختبار
                                </button>
                                <a href="{% url 'api:edit_ai_config' config.id %}" 
                                   class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        onclick="deleteConfig({{ config.id }}, '{{ provider.display_name }}')">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </div>
                        
                        <div id="test-result-{{ config.id }}" class="test-result"></div>
                    </div>
                </div>
                {% break %}
            {% endif %}
        {% empty %}
            <!-- Unconfigured Provider -->
            <div class="card provider-card position-relative">
                <div class="card-body text-center">
                    <div class="provider-icon bg-secondary bg-opacity-10">
                        {% if provider.name == 'gemini' %}
                            <i class="fab fa-google text-secondary"></i>
                        {% elif provider.name == 'openai' %}
                            <i class="fas fa-brain text-secondary"></i>
                        {% elif provider.name == 'claude' %}
                            <i class="fas fa-robot text-secondary"></i>
                        {% elif provider.name == 'huggingface' %}
                            <i class="fas fa-face-smile text-secondary"></i>
                        {% elif provider.name == 'ollama' %}
                            <i class="fas fa-server text-secondary"></i>
                        {% else %}
                            <i class="fas fa-cog text-secondary"></i>
                        {% endif %}
                    </div>
                    
                    <h5>{{ provider.display_name }}</h5>
                    <p class="text-muted small">{{ provider.description|default:"مقدم خدمة ذكاء اصطناعي" }}</p>
                    
                    <div class="alert alert-info small">
                        <i class="fas fa-info-circle me-1"></i>
                        لم يتم إعداد هذا المقدم بعد
                    </div>
                    
                    <a href="{% url 'api:add_ai_config' %}?provider={{ provider.id }}" 
                       class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إعداد الآن
                    </a>
                </div>
            </div>
        {% endfor %}
        {% endwith %}
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-robot fa-4x text-muted mb-3"></i>
            <h4>لا توجد مقدمو خدمات متاحون</h4>
            <p class="text-muted">يرجى إضافة مقدمي خدمات الذكاء الاصطناعي من لوحة الإدارة</p>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Help Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>كيفية الحصول على مفاتيح API
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fab fa-google me-2"></i>Google Gemini</h6>
                        <ol class="small">
                            <li>اذهب إلى <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></li>
                            <li>قم بتسجيل الدخول بحساب Google</li>
                            <li>انقر على "Create API Key"</li>
                            <li>انسخ المفتاح واحفظه بأمان</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-brain me-2"></i>OpenAI GPT</h6>
                        <ol class="small">
                            <li>اذهب إلى <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></li>
                            <li>قم بإنشاء حساب أو تسجيل الدخول</li>
                            <li>انقر على "Create new secret key"</li>
                            <li>انسخ المفتاح فوراً (لن يظهر مرة أخرى)</li>
                        </ol>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه أمني:</strong> لا تشارك مفاتيح API مع أي شخص آخر. احتفظ بها في مكان آمن.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف إعدادات <span id="providerName"></span>؟</p>
                <p class="text-danger small">لن تتمكن من التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function testConfig(configId, providerName) {
    const resultDiv = document.getElementById(`test-result-${configId}`);
    resultDiv.style.display = 'block';
    resultDiv.className = 'test-result';
    resultDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
            جاري اختبار الاتصال مع ${providerName}...
        </div>
    `;
    
    fetch('{% url "api:test_ai_config" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            config_id: configId,
            message: 'مرحبا، هذا اختبار للاتصال'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.className = 'test-result test-success';
            resultDiv.innerHTML = `
                <div class="d-flex align-items-start">
                    <i class="fas fa-check-circle me-2 mt-1"></i>
                    <div>
                        <strong>نجح الاختبار!</strong>
                        <br><small>النموذج: ${data.model}</small>
                        <br><small class="text-muted">الرد: ${data.response.substring(0, 100)}...</small>
                    </div>
                </div>
            `;
        } else {
            resultDiv.className = 'test-result test-error';
            resultDiv.innerHTML = `
                <div class="d-flex align-items-start">
                    <i class="fas fa-times-circle me-2 mt-1"></i>
                    <div>
                        <strong>فشل الاختبار</strong>
                        <br><small>${data.error}</small>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.className = 'test-result test-error';
        resultDiv.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="fas fa-times-circle me-2 mt-1"></i>
                <div>
                    <strong>خطأ في الاتصال</strong>
                    <br><small>تعذر الاتصال بالخادم</small>
                </div>
            </div>
        `;
    });
}

function deleteConfig(configId, providerName) {
    document.getElementById('providerName').textContent = providerName;
    document.getElementById('deleteForm').action = `{% url 'api:delete_ai_config' 0 %}`.replace('0', configId);
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
