"""
Django management command to setup default AI providers
"""

from django.core.management.base import BaseCommand
from api.models import AIProvider


class Command(BaseCommand):
    help = 'Setup default AI providers'

    def handle(self, *args, **options):
        providers_data = [
            {
                'name': 'gemini',
                'display_name': 'Google Gemini',
                'description': 'نموذج الذكاء الاصطناعي من Google، سريع ومتطور مع دعم النصوص والصور',
                'api_endpoint': 'https://generativelanguage.googleapis.com',
                'is_active': True,
                'requires_api_key': True,
            },
            {
                'name': 'openai',
                'display_name': 'OpenAI GPT',
                'description': 'نماذج GPT من OpenAI، متقدمة في فهم اللغة الطبيعية والمحادثة',
                'api_endpoint': 'https://api.openai.com',
                'is_active': True,
                'requires_api_key': True,
            },
            {
                'name': 'claude',
                'display_name': 'Anthropic Claude',
                'description': 'نموذج Claude من Anthropic، متخصص في المحادثات الآمنة والمفيدة',
                'api_endpoint': 'https://api.anthropic.com',
                'is_active': True,
                'requires_api_key': True,
            },
            {
                'name': 'huggingface',
                'display_name': 'Hugging Face',
                'description': 'منصة مفتوحة المصدر مع مجموعة واسعة من النماذج المجانية',
                'api_endpoint': 'https://api-inference.huggingface.co',
                'is_active': True,
                'requires_api_key': True,
            },
            {
                'name': 'ollama',
                'display_name': 'Ollama (محلي)',
                'description': 'تشغيل النماذج محلياً على الخادم، مجاني ولا يتطلب اتصال بالإنترنت',
                'api_endpoint': 'http://localhost:11434',
                'is_active': True,
                'requires_api_key': False,
            },
            {
                'name': 'custom',
                'display_name': 'مقدم خدمة مخصص',
                'description': 'إعداد مخصص لمقدم خدمة ذكاء اصطناعي آخر',
                'api_endpoint': '',
                'is_active': True,
                'requires_api_key': True,
            },
        ]

        created_count = 0
        updated_count = 0

        for provider_data in providers_data:
            provider, created = AIProvider.objects.get_or_create(
                name=provider_data['name'],
                defaults=provider_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ تم إنشاء مقدم الخدمة: {provider.display_name}')
                )
            else:
                # Update existing provider
                for key, value in provider_data.items():
                    if key != 'name':  # Don't update the name field
                        setattr(provider, key, value)
                provider.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'↻ تم تحديث مقدم الخدمة: {provider.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ تم الانتهاء! تم إنشاء {created_count} مقدم خدمة جديد وتحديث {updated_count} مقدم موجود.'
            )
        )
        
        self.stdout.write(
            self.style.HTTP_INFO(
                '\n📝 الخطوات التالية:'
                '\n1. اذهب إلى إعدادات الذكاء الاصطناعي'
                '\n2. أضف مفاتيح API للمقدمين المطلوبين'
                '\n3. اختبر الاتصال للتأكد من صحة الإعدادات'
            )
        )
