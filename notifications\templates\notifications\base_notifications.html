{% extends 'base_updated.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block sidebar %}
<div class="sidebar-wrapper">
    <div class="sidebar-header">
        <div class="d-flex justify-content-between align-items-center">
            <div class="logo">
                <a href="{% url 'notifications:dashboard' %}">
                    <i class="fas fa-bell me-2"></i>
                    <span>التنبيهات</span>
                </a>
            </div>
            <div class="toggler">
                <a href="#" class="sidebar-hide d-xl-none d-block"><i class="bi bi-x bi-middle"></i></a>
            </div>
        </div>
    </div>
    <div class="sidebar-menu">
        <ul class="menu">
            <li class="sidebar-title">القائمة</li>
            
            <li class="sidebar-item {% if request.path == '/notifications/' %}active{% endif %}">
                <a href="{% url 'notifications:dashboard' %}" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            
            <li class="sidebar-item {% if request.path == '/notifications/list/' %}active{% endif %}">
                <a href="{% url 'notifications:list' %}" class="sidebar-link">
                    <i class="fas fa-list"></i>
                    <span>جميع التنبيهات</span>
                </a>
            </li>
            
            <li class="sidebar-title">أنواع التنبيهات</li>
            
            <li class="sidebar-item {% if request.path == '/notifications/list/hr/' %}active{% endif %}">
                <a href="{% url 'notifications:list_by_type' 'hr' %}" class="sidebar-link">
                    <i class="fas fa-user-tie"></i>
                    <span>الموارد البشرية</span>
                </a>
            </li>
            
            <li class="sidebar-item {% if request.path == '/notifications/list/meetings/' %}active{% endif %}">
                <a href="{% url 'notifications:list_by_type' 'meetings' %}" class="sidebar-link">
                    <i class="fas fa-users"></i>
                    <span>الاجتماعات</span>
                </a>
            </li>
            
            <li class="sidebar-item {% if request.path == '/notifications/list/inventory/' %}active{% endif %}">
                <a href="{% url 'notifications:list_by_type' 'inventory' %}" class="sidebar-link">
                    <i class="fas fa-boxes"></i>
                    <span>المخزن</span>
                </a>
            </li>
            
            <li class="sidebar-item {% if request.path == '/notifications/list/purchase/' %}active{% endif %}">
                <a href="{% url 'notifications:list_by_type' 'purchase' %}" class="sidebar-link">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            
            <li class="sidebar-item {% if request.path == '/notifications/list/system/' %}active{% endif %}">
                <a href="{% url 'notifications:list_by_type' 'system' %}" class="sidebar-link">
                    <i class="fas fa-cogs"></i>
                    <span>النظام</span>
                </a>
            </li>
            
            <li class="sidebar-title">الإجراءات</li>
            
            <li class="sidebar-item">
                <a href="{% url 'notifications:mark_all_as_read' %}" class="sidebar-link mark-all-read">
                    <i class="fas fa-check-double"></i>
                    <span>تعليم الكل كمقروء</span>
                </a>
            </li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعليم الكل كمقروء
        const markAllReadBtn = document.querySelector('.mark-all-read');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                if (confirm('هل أنت متأكد من تعليم جميع التنبيهات كمقروءة؟')) {
                    fetch(this.href, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // تحديث الصفحة
                            window.location.reload();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                }
            });
        }
    });
</script>
{% endblock %}
