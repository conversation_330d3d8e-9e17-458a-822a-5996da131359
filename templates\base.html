{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ElDawliya System{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <!-- RTL CSS -->
    <link rel="stylesheet" href="{% static 'css/rtl.css' %}">
    <!-- Google Fonts (Cairo) -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --font-family: 'Cairo', sans-serif;
        }

        body {
            font-family: var(--font-family), system-ui, -apple-system, sans-serif;
            text-align: right;
        }

        /* RTL specific styles */
        .dropdown-menu {
            text-align: right;
        }

        .form-check {
            padding-right: 1.5em;
            padding-left: 0;
        }

        .form-check .form-check-input {
            float: right;
            margin-right: -1.5em;
            margin-left: 0;
        }

        .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-right: -1px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">نظام الدولية</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="{% url 'meetings:list' %}">الاجتماعات</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'tasks:list' %}">المهام</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'accounts:logout' %}">تسجيل الخروج</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    <div class="container mt-3">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        {% block content %}
        {% endblock %}
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy;  نظام الدولية إنترناشونال للطباعة. جميع الحقوق محفوظة.</p>
            <p>powered by <a href="https://www.eldawliya.com">ElDawliya</a></p></p>
            <p>
                <a href="#">سياسة الخصوصية</a> | <a href="#">شروط الاستخدام</a>
            </p>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Custom JavaScript -->
    <script>
        // Apply RTL specific JavaScript adjustments
        document.addEventListener('DOMContentLoaded', function() {
            // Set RTL for all elements that need it
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.textAlign = 'right';
            });

            // Adjust data tables for RTL if you're using them
            if (typeof $.fn.DataTable !== 'undefined') {
                $.extend(true, $.fn.DataTable.defaults, {
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
                    }
                });
            }

            // Fix any RTL issues with third-party plugins
            document.querySelectorAll('.fc-header-toolbar').forEach(toolbar => {
                toolbar.style.direction = 'rtl';
            });

            // Initialize tooltips with RTL support
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            if (tooltipTriggerList.length > 0) {
                tooltipTriggerList.forEach(tooltipTriggerEl => {
                    new bootstrap.Tooltip(tooltipTriggerEl, {
                        placement: 'auto',
                        container: 'body'
                    });
                });
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
