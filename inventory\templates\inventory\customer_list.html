<!-- templates/inventory/customer_list.html -->
{% extends 'inventory/base_inventory.html' %}
{% load inventory_permission_tags %}

{% block title %}قائمة العملاء - نظام إدارة المخزن{% endblock %}

{% block content %}
<div class="row ps-4">
    <div class="col-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    قائمة العملاء
                </h4>
                {% has_inventory_module_permission "customers" "add" as can_add_customer %}
                {% if can_add_customer %}
                <a href="{% url 'inventory:customer_add' %}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus-circle me-1"></i>
                    إضافة عميل جديد
                </a>
                {% endif %}
            </div>
            <div class="card-body p-4">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>كود العميل</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>{{ customer.id }}</td>
                                <td>{{ customer.name }}</td>
                                <td>{{ customer.phone|default:"-" }}</td>
                                <td>{{ customer.email|default:"-" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% has_inventory_module_permission "customers" "edit" as can_edit_customer %}
                                        {% if can_edit_customer %}
                                        <a href="{% url 'inventory:customer_edit' customer.id %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}

                                        {% has_inventory_module_permission "customers" "delete" as can_delete_customer %}
                                        {% if can_delete_customer %}
                                        <a href="{% url 'inventory:customer_delete' customer.id %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-exclamation-circle text-muted mb-2" style="font-size: 2rem;"></i>
                                    <p class="mb-0 text-muted">لا يوجد عملاء مسجلين حالياً</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}