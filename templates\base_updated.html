{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشركة الدولية إنترناشونال{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style_updated.css' %}">
    <style>
        /* Sidebar and layout styles */
        .with-sidebar {
            padding-left: 0;
            padding-right: 0;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .content-wrapper {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding-top: 20px;
            transition: all 0.3s;
            position: fixed;
            height: 100%;
            z-index: 100;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.5rem;
            color: white;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            margin-bottom: 5px;
        }

        .sidebar-menu-item a {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu-item a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu-item a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-menu-item.active a {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            margin-right: 250px;
            transition: all 0.3s;
        }

        .sidebar-collapsed .sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-header h3,
        .sidebar-collapsed .sidebar-menu-item a span {
            display: none;
        }

        .sidebar-collapsed .main-content {
            margin-right: 70px;
        }

        .navbar {
            background-color: #3498db;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
                transform: translateX(100%);
                right: -250px;
                z-index: 1050;
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar.show {
                width: 250px;
                padding-top: 20px;
                transform: translateX(0);
                right: 0;
            }

            /* Overlay for mobile sidebar */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body data-is-superuser="{{ request.user.is_superuser|yesno:'true,false' }}">
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar sticky-top">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-light me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'accounts:home' %}">
                        <i class="fas fa-building me-2"></i>
                        <span>نظام الشركة الدولية إنترناشونال</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Notifications Button -->
                    <div class="me-3">
                        <a href="{% url 'notifications:user_notifications' %}" class="btn btn-outline-light btn-sm position-relative">
                            <i class="fas fa-bell"></i>
                            {% if unread_notifications_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ unread_notifications_count }}
                                <span class="visually-hidden">تنبيهات غير مقروءة</span>
                            </span>
                            {% endif %}
                        </a>
                    </div>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i> {{ request.user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم</a></li>
                            <li><a class="dropdown-item" href="{% url 'notifications:user_notifications' %}"><i class="fas fa-bell me-2"></i> تنبيهاتي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-building me-2"></i> نظام الدولية</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>

                    <!-- API & AI Section -->
                    <li class="sidebar-menu-item">
                        <a href="{% url 'api:dashboard' %}">
                            <i class="fas fa-robot"></i>
                            <span>API والذكاء الاصطناعي</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'api:ai_chat' %}">
                            <i class="fas fa-comments"></i>
                            <span>محادثة AI</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/api/v1/docs/" target="_blank">
                            <i class="fas fa-book"></i>
                            <span>وثائق API</span>
                        </a>
                    </li>

                    <!-- HR Section -->
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:dashboard' %}">
                            <i class="fas fa-users"></i>
                            <span>الموارد البشرية</span>
                        </a>
                    </li>

                    <!-- Inventory Section -->
                    <li class="sidebar-menu-item">
                        <a href="{% url 'inventory:dashboard' %}">
                            <i class="fas fa-boxes"></i>
                            <span>المخزون</span>
                        </a>
                    </li>

                    <!-- Tasks Section -->
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:list' %}">
                            <i class="fas fa-tasks"></i>
                            <span>المهام</span>
                        </a>
                    </li>

                    <!-- Meetings Section -->
                    <li class="sidebar-menu-item">
                        <a href="{% url 'meetings:list' %}">
                            <i class="fas fa-calendar-alt"></i>
                            <span>الاجتماعات</span>
                        </a>
                    </li>

                    <!-- Admin Section -->
                    {% if user.is_staff %}
                    <li class="sidebar-menu-item">
                        <a href="{% url 'admin:index' %}">
                            <i class="fas fa-cog"></i>
                            <span>الإدارة</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </aside>

            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Page Header -->
                <div class="page-header mb-4">
                    <h1 class="page-title">{% block page_title %}{% endblock %}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}"><i class="fas fa-home"></i></a></li>
                            {% block breadcrumb %}{% endblock %}
                        </ol>
                    </nav>
                </div>

                <!-- Messages/Alerts -->
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Main Content -->
                <div class="content-container py-4">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5>نظام الدولية انترناشونال للطباعة والمنتجات الصحية</h5>
                        <p>نظام إدارة الاقسام والمهام للشركة الدولية</p>
                        <p>Mohamed Gamal</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-links">
                            <a href="#"><i class="fas fa-shield-alt me-1"></i> سياسة الخصوصية</a>
                            <span class="mx-2">|</span>
                            <a href="#"><i class="fas fa-file-contract me-1"></i> شروط الاستخدام</a>
                        </div>
                        <p class="mt-2">&copy; {{ current_year|default:"2025" }} نظام الدولية . جميع الحقوق محفوظة.</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scroll to top button -->
    <button id="scrollToTop" title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <!-- Sidebar Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const pageWrapper = document.querySelector('.page-wrapper');
            const sidebar = document.querySelector('.sidebar');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    pageWrapper.classList.toggle('sidebar-collapsed');

                    // For mobile devices
                    if (window.innerWidth <= 768) {
                        sidebar.classList.toggle('show');
                        overlay.classList.toggle('show');
                    }
                });
            }

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                pageWrapper.classList.remove('sidebar-collapsed');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    overlay.classList.remove('show');
                }
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
