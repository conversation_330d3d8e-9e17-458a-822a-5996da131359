{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">جلب بيانات الحضور من الماكينات</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    استخدم هذه الصفحة لجلب بيانات الحضور والانصراف من ماكينات البصمة.
                </div>

                <form method="post">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="{{ form.machine.id_for_label }}" class="form-label">{{ form.machine.label }}</label>
                        {{ form.machine }}
                        {% if form.machine.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.machine.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                        {{ form.start_date }}
                        {% if form.start_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.start_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.end_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download"></i> جلب البيانات
                        </button>
                        <a href="{% url 'employees:attendance_record_list' %}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> عرض سجلات الحضور
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">أدوات إضافية</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{% url 'employees:zk_device_connection' %}" class="btn btn-outline-primary">
                        <i class="fas fa-fingerprint me-2"></i> واجهة قارئ جهاز البصمة ZK
                    </a>
                    <a href="{% url 'employees:attendance_machine_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-cogs me-2"></i> إدارة ماكينات البصمة
                    </a>
                    <a href="{% url 'employees:attendance_record_list' %}" class="btn btn-outline-info">
                        <i class="fas fa-list-alt me-2"></i> سجلات الحضور والانصراف
                    </a>
                </div>

                <div class="alert alert-warning mt-4">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>ملاحظة:</strong> تأكد من تكوين إعدادات ماكينة البصمة بشكل صحيح قبل محاولة الاتصال بها.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
