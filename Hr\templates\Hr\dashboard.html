{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}لوحة تحكم الموارد البشرية - نظام الدولية{% endblock %}

{% block page_title %}لوحة تحكم الموارد البشرية{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم الموارد البشرية</li>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quick Search Functionality
        const quickSearch = document.getElementById('quickSearch');
        const quickSearchBtn = document.getElementById('quickSearchBtn');

        if (quickSearch && quickSearchBtn) {
            // Handle search button click
            quickSearchBtn.addEventListener('click', function() {
                handleSearch();
            });

            // Handle Enter key press in search input
            quickSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });

            function handleSearch() {
                const searchTerm = quickSearch.value.trim();
                if (searchTerm) {
                    // Redirect to employee search with the search term
                    window.location.href = "{% url 'Hr:employees:list' %}?search=" + encodeURIComponent(searchTerm);
                }
            }
        }

        // Alert Dismissal
        const dismissButtons = document.querySelectorAll('.dismiss-alert');
        dismissButtons.forEach(button => {
            button.addEventListener('click', function() {
                const alertItem = this.closest('.alert-item');
                const alertId = this.getAttribute('data-alert-id');

                // Add fade-out animation
                alertItem.classList.add('fade-out');

                // Remove from DOM after animation completes
                alertItem.addEventListener('animationend', function() {
                    alertItem.remove();

                    // Check if there are any alerts left
                    const remainingAlerts = document.querySelectorAll('.alert-item');
                    if (remainingAlerts.length === 0) {
                        // Add "no alerts" message
                        const listGroup = document.querySelector('.list-group');
                        if (listGroup) {
                            listGroup.innerHTML = `
                                <div class="text-center py-4">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <p class="mb-0">لا توجد تنبيهات في الوقت الحالي</p>
                                </div>
                            `;
                        }
                    }

                    // In a real application, you would make an AJAX call here to mark the alert as dismissed
                    console.log('Alert dismissed:', alertId);
                });
            });
        });

        // Interactive Card Effects
        const actionCards = document.querySelectorAll('.action-card');
        actionCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.action-icon');
                if (icon) {
                    icon.classList.add('pulse');

                    // Remove the animation class after it completes
                    icon.addEventListener('animationend', function() {
                        icon.classList.remove('pulse');
                    }, { once: true });
                }
            });
        });

        // Collapsible sections toggle icon rotation
        const collapsibleButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
        collapsibleButtons.forEach(button => {
            const targetId = button.getAttribute('data-bs-target');
            const target = document.querySelector(targetId);

            if (target) {
                target.addEventListener('shown.bs.collapse', function() {
                    const icon = button.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    }
                });

                target.addEventListener('hidden.bs.collapse', function() {
                    const icon = button.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });
            }
        });
    });
</script>

{% block content %}
<!-- Quick Access Toolbar -->
<div class="quick-access-toolbar mb-4">
    <div class="card shadow-sm border-0">
        <div class="card-body py-2">
            <div class="d-flex flex-wrap justify-content-between align-items-center">
                <div class="quick-actions d-flex">
                    <a href="{% url 'Hr:employees:create' %}" class="btn btn-sm btn-primary me-2">
                        <i class="fas fa-user-plus me-1"></i> إضافة موظف
                    </a>
                    <a href="{% url 'Hr:attendance_record_list' %}" class="btn btn-sm btn-outline-info me-2">
                        <i class="fas fa-clipboard-check me-1"></i> سجل الحضور
                    </a>
                    <a href="{% url 'Hr:payroll_calculate' %}" class="btn btn-sm btn-outline-success me-2">
                        <i class="fas fa-calculator me-1"></i> احتساب الرواتب
                    </a>
                    <a href="{% url 'Hr:reports:list' %}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-chart-bar me-1"></i> التقارير
                    </a>
                </div>
                <div class="quick-search">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" id="quickSearch" placeholder="بحث عن موظف..." aria-label="بحث عن موظف">
                        <button class="btn btn-sm btn-outline-secondary" type="button" id="quickSearchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Top Stats Cards Section with Interactive Elements -->
<div class="row mb-4 g-3">
    <div class="col-xl-3 col-md-6">
        <a href="{% url 'Hr:employees:list' %}" class="text-decoration-none">
            <div class="card shadow-sm stats-card primary h-100 highlight-card">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="stats-icon-wrapper bg-primary-subtle rounded-circle me-3 flex-shrink-0">
                        <i class="fas fa-users text-primary"></i>
                    </div>
                    <div>
                        <div class="stats-number fs-2 fw-bold">{{ total_employees }}</div>
                        <p class="stats-title text-muted mb-0">إجمالي الموظفين</p>
                    </div>
                    <div class="ms-auto">
                        <span class="badge bg-primary-subtle text-primary rounded-pill">الكل</span>
                    </div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-xl-3 col-md-6">
        <a href="{% url 'Hr:employees:list' %}?status=active" class="text-decoration-none">
            <div class="card shadow-sm stats-card success h-100 highlight-card">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="stats-icon-wrapper bg-success-subtle rounded-circle me-3 flex-shrink-0">
                        <i class="fas fa-user-check text-success"></i>
                    </div>
                    <div>
                        <div class="stats-number fs-2 fw-bold">{{ active_employees }}</div>
                        <p class="stats-title text-muted mb-0">الموظفين النشطين</p>
                    </div>
                    <div class="ms-auto">
                        <span class="badge bg-success-subtle text-success rounded-pill">نشط</span>
                    </div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-xl-3 col-md-6">
        <a href="{% url 'Hr:departments:list' %}" class="text-decoration-none">
            <div class="card shadow-sm stats-card info h-100 highlight-card">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="stats-icon-wrapper bg-info-subtle rounded-circle me-3 flex-shrink-0">
                        <i class="fas fa-building text-info"></i>
                    </div>
                    <div>
                        <div class="stats-number fs-2 fw-bold">{{ departments_count }}</div>
                        <p class="stats-title text-muted mb-0">الأقسام</p>
                    </div>
                    <div class="ms-auto">
                        <i class="fas fa-angle-left text-muted"></i>
                    </div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-xl-3 col-md-6">
        <a href="{% url 'Hr:jobs:list' %}" class="text-decoration-none">
            <div class="card shadow-sm stats-card warning h-100 highlight-card">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="stats-icon-wrapper bg-warning-subtle rounded-circle me-3 flex-shrink-0">
                        <i class="fas fa-briefcase text-warning"></i>
                    </div>
                    <div>
                        <div class="stats-number fs-2 fw-bold">{{ jobs_count }}</div>
                        <p class="stats-title text-muted mb-0">الوظائف</p>
                    </div>
                    <div class="ms-auto">
                        <i class="fas fa-angle-left text-muted"></i>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>

<!-- Alert Section with Enhanced UI -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-gradient-warning-subtle py-3 d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="alert-icon-wrapper bg-warning text-white rounded-circle me-2">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h5 class="mb-0 fw-semibold">التنبيهات الهامة</h5>
                </div>
                <div>
                    <a href="{% url 'Hr:alerts:list' %}" class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-external-link-alt me-1"></i> عرض الكل
                    </a>
                    <button class="btn btn-sm btn-outline-secondary ms-2" data-bs-toggle="collapse" data-bs-target="#alertsCollapse" aria-expanded="true">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="alertsCollapse">
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if alerts %}
                            {% for alert in alerts %}
                            <div class="list-group-item py-3 px-4 alert-item">
                                <div class="d-flex align-items-center">
                                    <div class="alert-icon flex-shrink-0 me-3">
                                        {% if alert.type == 'contract' %}
                                        <div class="icon-wrapper bg-danger-subtle text-danger rounded-circle">
                                            <i class="fas fa-file-contract"></i>
                                        </div>
                                        {% elif alert.type == 'health_card' %}
                                        <div class="icon-wrapper bg-warning-subtle text-warning rounded-circle">
                                            <i class="fas fa-id-card"></i>
                                        </div>
                                        {% elif alert.type == 'insurance' %}
                                        <div class="icon-wrapper bg-info-subtle text-info rounded-circle">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        {% elif alert.type == 'task' %}
                                        <div class="icon-wrapper bg-primary-subtle text-primary rounded-circle">
                                            <i class="fas fa-tasks"></i>
                                        </div>
                                        {% else %}
                                        <div class="icon-wrapper bg-secondary-subtle text-secondary rounded-circle">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <h6 class="mb-1">{{ alert.title }}</h6>
                                        <p class="text-muted mb-1 small">{{ alert.description }}</p>
                                        {% if alert.employee %}
                                        <div>
                                            <a href="{% url 'Hr:employees:detail' alert.employee.emp_id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i> عرض الموظف
                                            </a>
                                            <span class="badge bg-light text-dark">{{ alert.days_remaining }} أيام متبقية</span>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="ms-auto">
                                        <button type="button" class="btn-close dismiss-alert" data-alert-id="{{ alert.id }}"></button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="mb-0">لا توجد تنبيهات في الوقت الحالي</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Section -->
<div class="row g-4">
    <!-- Left Column -->
    <div class="col-lg-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-users me-2 text-primary"></i>
                    إدارة الموظفين
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-6 col-xl-4">
                        <a href="{% url 'Hr:employees:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">قائمة الموظفين</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة بيانات الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6 col-xl-4">
                        <a href="{% url 'Hr:employees:create' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-success-subtle text-success rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">إضافة موظف جديد</h6>
                                    <p class="text-muted small mb-0">تسجيل بيانات موظف جديد</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6 col-xl-4">
                        <a href="{% url 'Hr:departments:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-info-subtle text-info rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">الأقسام</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة الأقسام</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6 col-xl-4">
                        <a href="{% url 'Hr:jobs:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-warning-subtle text-warning rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">الوظائف</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة المسميات الوظيفية</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6 col-xl-4">
                        <a href="{% url 'Hr:notes:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-secondary-subtle text-secondary rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-sticky-note"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">ملاحظات الموظفين</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة ملاحظات الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6 col-xl-4">
                        <a href="{% url 'Hr:files:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-danger-subtle text-danger rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">ملفات الموظفين</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة ملفات الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-money-bill-wave me-2 text-primary"></i>
                    إدارة الرواتب
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="{% url 'Hr:salary_item_list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-list-ul"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">بنود الرواتب</h6>
                                    <p class="text-muted small mb-0">إدارة بنود الاستحقاقات والاستقطاعات</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{% url 'Hr:employee_salary_item_list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-success-subtle text-success rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-user-tag"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">بنود رواتب الموظفين</h6>
                                    <p class="text-muted small mb-0">تخصيص بنود الرواتب للموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{% url 'Hr:payroll_period_list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-info-subtle text-info rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">فترات الرواتب</h6>
                                    <p class="text-muted small mb-0">إدارة فترات صرف الرواتب</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{% url 'Hr:payroll_calculate' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-warning-subtle text-warning rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">حساب الرواتب</h6>
                                    <p class="text-muted small mb-0">احتساب رواتب الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transportation Section -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-car me-2 text-primary"></i>
                    نقل الموظفين
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="{% url 'Hr:cars:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-car"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">سيارات النقل</h6>
                                    <p class="text-muted small mb-0">إدارة سيارات نقل الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{% url 'Hr:pickup_points:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-success-subtle text-success rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-map-marked-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">نقاط التجمع</h6>
                                    <p class="text-muted small mb-0">إدارة نقاط تجمع الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column -->
    <div class="col-lg-4">
        <!-- Quick Links Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-link me-2 text-primary"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="{% url 'Hr:reports:list' %}" class="list-group-item list-group-item-action py-3 px-4">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper bg-info-subtle text-info rounded-circle me-3">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">التقارير</h6>
                                <p class="text-muted mb-0 small">عرض وطباعة تقارير متنوعة</p>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'Hr:analytics:dashboard' %}" class="list-group-item list-group-item-action py-3 px-4">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper bg-success-subtle text-success rounded-circle me-3">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">تحليل البيانات</h6>
                                <p class="text-muted mb-0 small">إحصائيات ورسوم بيانية</p>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'Hr:org_chart' %}" class="list-group-item list-group-item-action py-3 px-4">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper bg-warning-subtle text-warning rounded-circle me-3">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">الهيكل التنظيمي</h6>
                                <p class="text-muted mb-0 small">عرض الهيكل التنظيمي للشركة</p>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'Hr:alerts:list' %}" class="list-group-item list-group-item-action py-3 px-4">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper bg-danger-subtle text-danger rounded-circle me-3">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">التنبيهات</h6>
                                <p class="text-muted mb-0 small">عرض وإدارة جميع التنبيهات</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Attendance Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-clock me-2 text-primary"></i>
                    الحضور والانصراف
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="d-grid gap-3">
                    <a href="{% url 'Hr:attendance_rule_list' %}" class="card action-card text-decoration-none shadow-hover">
                        <div class="card-body d-flex align-items-center p-3">
                            <div class="action-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-dark">قواعد الحضور</h6>
                                <p class="text-muted small mb-0">إدارة قواعد الحضور والانصراف</p>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'Hr:attendance_record_list' %}" class="card action-card text-decoration-none shadow-hover">
                        <div class="card-body d-flex align-items-center p-3">
                            <div class="action-icon bg-warning-subtle text-warning rounded-circle me-3 flex-shrink-0">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-dark">سجلات الحضور</h6>
                                <p class="text-muted small mb-0">عرض وإدارة سجلات الحضور والانصراف</p>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'Hr:official_holiday_list' %}" class="card action-card text-decoration-none shadow-hover">
                        <div class="card-body d-flex align-items-center p-3">
                            <div class="action-icon bg-danger-subtle text-danger rounded-circle me-3 flex-shrink-0">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-dark">الإجازات الرسمية</h6>
                                <p class="text-muted small mb-0">إدارة الإجازات والعطلات الرسمية</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Tasks Card -->
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-tasks me-2 text-primary"></i>
                    المهام
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="d-grid gap-3">
                    <a href="{% url 'Hr:tasks:list' %}" class="card action-card text-decoration-none shadow-hover">
                        <div class="card-body d-flex align-items-center p-3">
                            <div class="action-icon bg-info-subtle text-info rounded-circle me-3 flex-shrink-0">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-dark">مهام الموظفين</h6>
                                <p class="text-muted small mb-0">إدارة مهام الموظفين</p>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'Hr:hr_tasks:list' %}" class="card action-card text-decoration-none shadow-hover">
                        <div class="card-body d-flex align-items-center p-3">
                            <div class="action-icon bg-success-subtle text-success rounded-circle me-3 flex-shrink-0">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-dark">مهام الموارد البشرية</h6>
                                <p class="text-muted small mb-0">إدارة مهام قسم الموارد البشرية</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات عامة */
    body {
        background-color: #f8f9fa;
    }
    .card {
        border: none;
        transition: box-shadow 0.3s ease-in-out, transform 0.2s ease;
        border-radius: 10px;
        overflow: hidden;
    }
    .card:hover {
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.1)!important;
    }
    .card-header {
        background-color: #f8f9fa;
    }

    /* Quick Access Toolbar */
    .quick-access-toolbar .card {
        border-radius: 8px;
        background: linear-gradient(to right, #f8f9fa, #ffffff);
    }
    .quick-search .input-group {
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    .quick-search .input-group:focus-within {
        box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    }

    /* تنسيق بطاقات الإحصائيات المحسنة */
    .highlight-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .highlight-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1)!important;
    }
    .stats-icon-wrapper {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
    }
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-title {
        font-size: 0.9rem;
    }

    /* تنسيق قسم التنبيهات */
    .bg-gradient-warning-subtle {
        background: linear-gradient(45deg, #fff8e1, #fffde7);
    }
    .alert-icon-wrapper {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }
    .alert-item {
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
    }
    .alert-item:hover {
        border-left-color: #ffc107;
        background-color: rgba(255,193,7,0.05);
    }
    .icon-wrapper {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    /* تنسيق بطاقات الإجراءات */
    .action-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }
    .action-card {
        border: 1px solid #e9ecef;
        transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        border-radius: 10px;
    }
    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1)!important;
    }
    .shadow-hover:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    /* تنسيق قائمة الروابط السريعة */
    .list-group-item {
        border-left: 3px solid transparent;
        transition: all 0.2s ease-in-out;
        padding: 0.75rem 1rem;
    }
    .list-group-item:hover {
        background-color: rgba(0,123,255,0.05);
        border-left-color: #0d6efd;
    }

    /* تأثيرات متنوعة */
    .badge {
        font-weight: 500;
        padding: 0.4rem 0.7rem;
    }
    .bg-primary-subtle {
        background-color: rgba(13, 110, 253, 0.15);
    }
    .bg-success-subtle {
        background-color: rgba(25, 135, 84, 0.15);
    }
    .bg-info-subtle {
        background-color: rgba(13, 202, 240, 0.15);
    }
    .bg-warning-subtle {
        background-color: rgba(255, 193, 7, 0.15);
    }
    .bg-danger-subtle {
        background-color: rgba(220, 53, 69, 0.15);
    }
    .bg-secondary-subtle {
        background-color: rgba(108, 117, 125, 0.15);
    }

    /* تأثيرات التفاعل */
    .fade-out {
        animation: fadeOut 0.5s ease forwards;
    }
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; height: 0; padding: 0; margin: 0; border: 0; }
    }
    .pulse {
        animation: pulse 0.5s ease-in-out;
    }
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
</style>
{% endblock %}
