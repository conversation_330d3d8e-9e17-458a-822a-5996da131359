<!-- templates/inventory/supplier_list.html -->
{% extends 'inventory/base_inventory.html' %}
{% load inventory_permission_tags %}

{% block title %}قائمة الموردين - نظام إدارة المخزن{% endblock %}

{% block content %}
<div class="row ps-4">
    <div class="col-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    قائمة الموردين
                </h4>
                {% if perms.inventory.add_supplier or user.is_superuser %}
                    <a href="{% url 'inventory:add_supplier' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة مورد جديد
                    </a>
                {% endif %}
            </div>
            <div class="card-body p-4">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>كود المورد</th>
                                <th>اسم المورد</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers %}
                            <tr>
                                <td>{{ supplier.id }}</td>
                                <td>{{ supplier.name }}</td>
                                <td>{{ supplier.phone|default:"-" }}</td>
                                <td>{{ supplier.email|default:"-" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if perms.inventory.change_supplier or user.is_superuser %}
                                        <a href="{% url 'inventory:edit_supplier' supplier.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}

                                        {% if perms.inventory.delete_supplier or user.is_superuser %}
                                        <button type="button" class="btn btn-sm btn-danger delete-supplier" 
                                                data-supplier-id="{{ supplier.id }}"
                                                data-supplier-name="{{ supplier.name }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-exclamation-circle text-muted mb-2" style="font-size: 2rem;"></i>
                                    <p class="mb-0 text-muted">لا يوجد موردين مسجلين حالياً</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}