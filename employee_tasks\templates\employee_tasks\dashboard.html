{% extends 'employee_tasks/base_employee_tasks.html' %}

{% block title %}لوحة تحكم مهام الموظفين - نظام الدولية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0 text-gray-800">لوحة تحكم مهام الموظفين</h1>
            <p class="text-muted">مرحباً بك في نظام إدارة مهام الموظفين</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6">
            <div class="stat-card primary">
                <div class="stat-number">{{ total_tasks }}</div>
                <div class="stat-title">إجمالي المهام</div>
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card warning">
                <div class="stat-number">{{ pending_tasks }}</div>
                <div class="stat-title">مهام قيد الانتظار</div>
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card info">
                <div class="stat-number">{{ in_progress_tasks }}</div>
                <div class="stat-title">مهام قيد التنفيذ</div>
                <div class="stat-icon">
                    <i class="fas fa-spinner"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card success">
                <div class="stat-number">{{ completed_tasks }}</div>
                <div class="stat-title">مهام مكتملة</div>
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">المهام الحديثة</h5>
                    <a href="{% url 'employee_tasks:task_list' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-list me-1"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_tasks %}
                        <div class="list-group">
                            {% for task in recent_tasks %}
                                <a href="{% url 'employee_tasks:task_detail' task.pk %}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ task.title }}</h6>
                                        <small>{{ task.created_at|date:"Y-m-d" }}</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i> {{ task.created_by.username }}
                                            </small>
                                            {% if task.assigned_to %}
                                                <small class="text-muted ms-2">
                                                    <i class="fas fa-user-check me-1"></i> {{ task.assigned_to.username }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        <span class="badge badge-{{ task.status }}">
                                            {{ task.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="progress mt-2" style="height: 5px;">
                                        <div class="progress-bar bg-{{ task.priority }}" role="progressbar" style="width: {{ task.progress }}%;" aria-valuenow="{{ task.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد مهام حديثة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Upcoming Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">المهام القادمة</h5>
                    <a href="{% url 'employee_tasks:calendar' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-calendar-alt me-1"></i> التقويم
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_tasks %}
                        <div class="list-group">
                            {% for task in upcoming_tasks %}
                                <a href="{% url 'employee_tasks:task_detail' task.pk %}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ task.title }}</h6>
                                        <small>{{ task.due_date|date:"Y-m-d" }}</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i> {{ task.created_by.username }}
                                            </small>
                                            {% if task.assigned_to %}
                                                <small class="text-muted ms-2">
                                                    <i class="fas fa-user-check me-1"></i> {{ task.assigned_to.username }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        <span class="badge badge-{{ task.priority }}">
                                            {{ task.get_priority_display }}
                                        </span>
                                    </div>
                                    <div class="progress mt-2" style="height: 5px;">
                                        <div class="progress-bar bg-{{ task.priority }}" role="progressbar" style="width: {{ task.progress }}%;" aria-valuenow="{{ task.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد مهام قادمة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Overdue Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        المهام المتأخرة ({{ overdue_tasks }})
                    </h5>
                </div>
                <div class="card-body">
                    {% if overdue_tasks > 0 %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            لديك {{ overdue_tasks }} مهمة متأخرة. يرجى مراجعة المهام وتحديث حالتها.
                        </div>
                        <a href="{% url 'employee_tasks:task_list' %}?status=pending" class="btn btn-danger">
                            <i class="fas fa-search me-1"></i> عرض المهام المتأخرة
                        </a>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            ليس لديك أي مهام متأخرة. أحسنت!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">التصنيفات</h5>
                    <a href="{% url 'employee_tasks:category_list' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-tags me-1"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if categories %}
                        <div class="list-group">
                            {% for category in categories %}
                                <a href="{% url 'employee_tasks:task_list' %}?category={{ category.id }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="{{ category.icon }}" style="color: {{ category.color }};"></i>
                                        <span class="ms-2">{{ category.name }}</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">{{ category.task_count }}</span>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد تصنيفات.
                        </div>
                        <a href="{% url 'employee_tasks:category_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> إنشاء تصنيف جديد
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:task_create' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-plus-circle me-2"></i> إنشاء مهمة جديدة
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:my_tasks' %}" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-user-check me-2"></i> عرض مهامي
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:calendar' %}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-calendar-alt me-2"></i> عرض التقويم
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:analytics' %}" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-bar me-2"></i> عرض التحليلات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any dashboard-specific JavaScript here
</script>
{% endblock %}
