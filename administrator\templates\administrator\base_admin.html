{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة تحكم مدير النظام{% endblock %} - نظام الدولية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #3f51b5;
            --secondary-color: #303f9f;
            --primary-light: #c5cae9;
            --accent-color: #ff5722;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }

        .navbar-light .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: white;
        }

        .with-sidebar {
            padding-left: 0;
            padding-right: 0;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .content-wrapper {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 250px;
            background-color: var(--secondary-color);
            color: white;
            padding-top: 20px;
            transition: all 0.3s;
            position: fixed;
            height: 100%;
            z-index: 100;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.5rem;
            color: white;
            font-weight: 600;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            margin-bottom: 5px;
        }

        .sidebar-menu-item a {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu-item a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu-item a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-menu-item.active a {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            margin-right: 250px;
            transition: all 0.3s;
        }

        .sidebar-collapsed .sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-header h3,
        .sidebar-collapsed .sidebar-menu-item a span {
            display: none;
        }

        .sidebar-collapsed .main-content {
            margin-right: 70px;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }

        .card-title {
            margin-bottom: 0;
            font-weight: bold;
            color: var(--secondary-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }

        .btn-accent:hover {
            background-color: #e64a19;
            border-color: #e64a19;
            color: white;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
                transform: translateX(100%);
                right: -250px;
                z-index: 1050;
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar.show {
                width: 250px;
                padding-top: 20px;
                transform: translateX(0);
                right: 0;
            }

            /* Overlay for mobile sidebar */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-light me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'administrator:admin_dashboard' %}">
                        <i class="fas fa-cogs me-2"></i>
                        <span>لوحة تحكم مدير النظام</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <div class="user-info me-3 d-flex align-items-center">
                        <i class="fas fa-user-circle text-light me-2 fs-5"></i>
                        <span class="text-light fw-bold">{{ user.username }}</span>
                    </div>
                    <a href="{% url 'accounts:logout' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-cogs me-2"></i> إدارة النظام</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item {% if request.path == '/administrator/' %}active{% endif %}">
                        <a href="{% url 'administrator:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if '/administrator/settings/' in request.path and not 'database' in request.path %}active{% endif %}">
                        <a href="{% url 'administrator:settings' %}">
                            <i class="fas fa-cogs"></i>
                            <span>إعدادات النظام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if '/administrator/settings/database/' in request.path %}active{% endif %}">
                        <a href="{% url 'administrator:database_settings' %}">
                            <i class="fas fa-database"></i>
                            <span>إعدادات قاعدة البيانات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if '/administrator/departments/' in request.path %}active{% endif %}">
                        <a href="{% url 'administrator:department_list' %}">
                            <i class="fas fa-building"></i>
                            <span>الأقسام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if '/administrator/modules/' in request.path %}active{% endif %}">
                        <a href="{% url 'administrator:module_list' %}">
                            <i class="fas fa-puzzle-piece"></i>
                            <span>الوحدات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if '/administrator/permissions/' in request.path or '/administrator/groups/' in request.path or '/administrator/users/' in request.path %}active{% endif %}">
                        <a href="{% url 'administrator:permission_dashboard' %}">
                            <i class="fas fa-user-shield"></i>
                            <span>إدارة الصلاحيات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if '/audit/' in request.path %}active{% endif %}">
                        <a href="{% url 'audit:audit_list' %}">
                            <i class="fas fa-clipboard-check"></i>
                            <span>سجلات التدقيق</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>العودة إلى الصفحة الرئيسية</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Content -->
            <div class="main-content">
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Page Header -->
                <div class="page-header mb-4">
                    <h2 class="mb-0">
                        <i class="fas fa-{% block page_icon %}cog{% endblock %} me-2"></i>
                        {% block page_header %}لوحة تحكم مدير النظام{% endblock %}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            {% block breadcrumb %}
                            <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">لوحة تحكم مدير النظام</li>
                            {% endblock %}
                        </ol>
                    </nav>
                </div>

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add Bootstrap classes to form elements
            document.querySelectorAll('input, select, textarea').forEach(function(input) {
                input.classList.add('form-control');
            });

            document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(function(input) {
                input.classList.remove('form-control');
                input.classList.add('form-check-input');
            });

            document.querySelectorAll('button[type="submit"]').forEach(function(button) {
                if (!button.classList.contains('btn')) {
                    button.classList.add('btn', 'btn-primary', 'mt-3');
                }
            });

            // Add form-select to select elements
            document.querySelectorAll('select').forEach(function(select) {
                select.classList.add('form-select');
            });

            // Sidebar Toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const pageWrapper = document.querySelector('.page-wrapper');
            const sidebar = document.querySelector('.sidebar');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    pageWrapper.classList.toggle('sidebar-collapsed');

                    // For mobile devices
                    if (window.innerWidth <= 768) {
                        sidebar.classList.toggle('show');
                        overlay.classList.toggle('show');
                    }
                });
            }

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                pageWrapper.classList.remove('sidebar-collapsed');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    overlay.classList.remove('show');
                }
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Highlight active menu item
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-menu-item');

            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && currentPath.includes(link.getAttribute('href'))) {
                    item.classList.add('active');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
